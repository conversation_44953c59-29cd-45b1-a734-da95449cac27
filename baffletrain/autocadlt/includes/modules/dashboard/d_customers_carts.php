<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/



  class d_customers_carts {
    var $code = 'd_customers_carts';
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->title = MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_TITLE;
      $this->description = MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_DESCRIPTION;

      if ( defined('MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_STATUS') ) {
        $this->sort_order = MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_SORT_ORDER;
        $this->enabled = (MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_STATUS == 'True');
      }
    }



    function getOutput() {
		global $customer_id;
      $output = '<table class="table table-condensed small" border="0" width="100%" cellspacing="0" cellpadding="4">' .
			   '  <thead>' .	
                '  <tr>' .
                '    <th>' . MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_TITLE . '</th>' .
                '    <th align="right">' . MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_DATE . '</th>' .
                '  </tr>' .
				'</thead><tbody>';

    

	$customers_query = tep_db_query("
		SELECT * 
		FROM 	`customers_basket` cb 
		JOIN 	`customers` c on `c`.`customers_id`= `cb`.`customers_id`
		JOIN 	`address_book` ab on `ab`.`address_book_id` = `c`.`customers_default_address_id` 
		ORDER BY `customers_basket_date_added` DESC 
		LIMIT 24;");
      while ($customers = tep_db_fetch_array($customers_query)) {
		print_rr($customers);
		$output .=	'  <tr class="cart_customer" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);">' .
					'    <td class="">' . $customers['entry_company'] . ': <a href="' . tep_href_link('customers.php', 'cID=' . (int)$customers['customers_id'] . '&action=edit') . '">' . tep_output_string_protected($customers['customers_firstname'] . ' ' . $customers['customers_lastname']) . '</a></td>' .
					'    <td align="right">'  . $customers['customers_basket_date_added'] . '</td>' .
					' </tr>' . 
					'<tr >' . 
					'	<td style="padding: 0;border: 0;" colspan="2">' .		  
					'	   <div style="display:none"><table class="table table-striped table-condensed">' .
					'			<tbody>'; 
		  

//$cart_query = tep_db_query("SELECT * FROM `customers_basket` cb JOIN `customers` c on `c`.`customers_id`= `cb`.`customers_id` where `cb`.`customers_id` = " . $customers_query['customers_id']);
    $customer_id = (int)$customers['customers_id'];
    $cart = new shoppingCart();
	$products = $cart->restore_contents();
    $products = $cart->get_products();
    for ($i=0, $n=sizeof($products); $i<$n; $i++) {
// Push all attributes information in an array
      if (isset($products[$i]['attributes']) && is_array($products[$i]['attributes'])) {
        foreach($products[$i]['attributes'] as $option => $value) {
          echo tep_draw_hidden_field('id[' . $products[$i]['id'] . '][' . $option . ']', $value);
          $attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix
                                      from products_options popt, products_options_values poval, products_attributes pa
                                      where pa.products_id = '" . (int)$products[$i]['id'] . "'
                                       and pa.options_id = '" . (int)$option . "'
                                       and pa.options_id = popt.products_options_id
                                       and pa.options_values_id = '" . (int)$value . "'
                                       and pa.options_values_id = poval.products_options_values_id
                                       and popt.language_id = '" . (int)$languages_id . "'
                                       and poval.language_id = '" . (int)$languages_id . "'");
          $attributes_values = tep_db_fetch_array($attributes);

          $products[$i][$option]['products_options_name'] = $attributes_values['products_options_name'];
          $products[$i][$option]['options_values_id'] = $value;
          $products[$i][$option]['products_options_values_name'] = $attributes_values['products_options_values_name'];
          $products[$i][$option]['options_values_price'] = $attributes_values['options_values_price'];
          $products[$i][$option]['price_prefix'] = $attributes_values['price_prefix'];
        }
      }
    }
?>   
<?php
    
    for ($i=0, $n=sizeof($products); $i<$n; $i++) {
      $output .= '<tr>';   
      $output .= '<td valign="top">' . $products[$i]['quantity'] . ' x <a href="' . tep_href_link('product_info.php', 'products_id=' . $products[$i]['id']) . '"><strong>' . $products[$i]['name'] . '</strong></a>';


      if (isset($products[$i]['attributes']) && is_array($products[$i]['attributes'])) {
        foreach($products[$i]['attributes'] as $option => $value) {
          $output .= '<small><i>' . $products[$i][$option]['products_options_name'] . ' ' . $products[$i][$option]['products_options_values_name'] . '</i></small>';
        }
      }

        $output .= '</td></tr>';

     
    }
	
	 $output .= '</tbody></table></div>';
?>

				
	<?php
		
		 $output .= '</td></tr>';

      }

      $output .= '</tbody></table>';
	$output .= "
	
	<script type='text/javascript'>
	var fadeSpeed = 400;
	var slideSpeed= 300;
    $(document).ready(function() {
    // Attach click event listener to all rows with the class .customer
    $('.cart_customer').click(function() {
        // Toggle the next row with a slide effect
		row = $(this).next();
				row.children('td, th')
				.children('div')
				.slideToggle();
			
		});
	});
	</script>";

		
      return $output;
    }

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_STATUS');
    }

    function install() {
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Customers Module', 'MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_STATUS', 'True', 'Do you want to show the newest customers on the dashboard?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove() {
      tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_STATUS', 'MODULE_ADMIN_DASHBOARD_CUSTOMERS_CARTS_SORT_ORDER');
    }
  }
?>
