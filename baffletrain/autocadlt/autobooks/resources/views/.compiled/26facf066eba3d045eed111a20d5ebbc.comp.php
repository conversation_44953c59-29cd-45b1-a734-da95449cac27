<?php namespace edgeTemplate\view_26facf066eba3d045eed111a20d5ebbc;use edge\Edge;?><?php extract(['edge_manifest' => array (
)]);;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>// Data table template
// This template will be used to generate data table views

// Headers and rows will be replaced with actual data


// Render the data table
<?php
$headers = array (
);
$rows = array (
);
Edge::render('data-table', [
    'headers' => $headers,
    'rows' => $rows,
    'striped' => true,
    'hover' => true
]);
?><?php } ?>