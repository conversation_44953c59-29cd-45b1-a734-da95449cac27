<?php
namespace system;
use edge\edge;

class router {
    protected static tcs $tcs;
    public static $routes = array();

    public function __construct($tcs = 'tcs') {
        self::$tcs = $tcs;
    }


    public static function route() {
        $app_path = APP_PATH;
        $current_page = CURRENT_PAGE;        // Check permissions before routing

        print_rr("starting route for {$app_path}/{$current_page}");
        //if (defined(SOURCE_PATH)) $src_app_path = SOURCE_PATH . SOURCE_PAGE;
        $endpoint = CURRENT_PAGE;
        $public = false;
        $target_edge = "layout-main";
        $function_call = false;
        $route_error = false;
        $error_message = '';

        // Special route handlers
        $special_routes = ["scheduled", "api", "settings", "reset-password", "login", "logout"];
        $is_special_route = in_array(TOP_LEVEL, $special_routes);

        // Handle special routes first
        switch (TOP_LEVEL) {
            case "scheduled":
                $sched_file = "resources/scheduled_scripts/{$current_page}.sched.php";
                if (file_exists($sched_file)) {
                    $endpoint = $sched_file;
                } else {
                    $error_message = "Scheduled script not found: {$sched_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                break;
            case "api":
                $parts = explode('/', $app_path);
                $function_call = array_pop($parts);
                $api_result = self::get_api(implode('/', $parts));

                if ($api_result['status'] === 'success') {
                    $endpoint = $api_result['path'];
                } else {
                    $error_message = $api_result['message'];
                    $route_error = true;
                }
                print_rr([
                    'parts' => $parts,
                    'endpoint' => $endpoint,
                    'function_call' => $function_call,
                    'api_result' => $api_result,
                    'api_result_status' => $api_result['status'] ?? 'no status'
                ], "endpointy");
                $target_edge = "layout-api";
                break;
            case "settings":
                $settings_file = str_replace('//', '/', "resources/views/" . SOURCE_APP_PATH . "/" . SOURCE_PAGE . "/" . SOURCE_PAGE . ".settings.php");
                if (file_exists($settings_file)) {
                    $endpoint = $settings_file;
                } else {
                    $error_message = "Settings file not found: {$settings_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                $target_edge = "layout-view";
                break;
            case "reset-password":
                // Show reset password page
                $reset_file = "system/views/login/reset-password.view.php";
                if (file_exists($reset_file)) {
                    $endpoint = $reset_file;
                } else {
                    $error_message = "Reset password file not found: {$reset_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                $target_edge = "layout-blank";
                $public = true;
                break;
            case "login":
                if (users::checkAuth()) {
                    // If already authenticated, do a clean redirect to dashboard
                    if (!headers_sent()) {
                        header("HTTP/1.1 303 See Other");
                        header("Location: " . rtrim(APP_ROOT, '/'));
                        exit();
                    } else {
                        echo "<script>window.location.href = '" . rtrim(APP_ROOT, '/') . "';</script>";
                        exit();
                    }
                } else {
                    // Show login page
                    $login_file = "system/views/login/login.view.php";
                    if (file_exists($login_file)) {
                        $endpoint = $login_file;
                    } else {
                        $error_message = "Login file not found: {$login_file}";
                        tcs_log($error_message,'router_errors');
                        $route_error = true;
                    }
                    $target_edge = "layout-blank";
                    $public = true;
                }
                break;

            case "logout":
                users::logout();
                $login_file = "system/views/login/login.view.php";
                if (file_exists($login_file)) {
                    $endpoint = $login_file;
                } else {
                    $error_message = "Login file not found after logout: {$login_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                $target_edge = "layout-blank";
                break;
        }

        // For all non-special routes (including the default case), treat as a view route
        if (!$is_special_route) {
            // Handle as a view route (previously get_view)
            $route_result = self::get_route($app_path);

            if ($route_result['status'] === 'success') {
                $endpoint = $route_result['path'];
            } else {
                $error_message = $route_result['message'];
                $route_error = true;
            }

            // Check if this is an HTMX request
            $is_htmx_request = isset($_SERVER['HTTP_HX_REQUEST']);

            // Only use layout-view for HTMX requests
            // For direct URL access (non-HTMX), always use layout-main
            if ($is_htmx_request) {
                $target_edge = 'layout-view';
            }
            // Otherwise, keep the default layout-main set at the beginning of the method
        }

        print_rr($endpoint, "endpoint");
       
       
        if ($route_error && isset($_SERVER['HTTP_HX_REQUEST'])) {
            // For HTMX requests, return a simple error message without reloading the whole app
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . addslashes($error_message) . '"}}');
            return "<div class='p-4 bg-red-100 border border-red-400 text-red-700 rounded'>
                    <h3 class='font-bold'>Error</h3>
                    <p>{$error_message}</p>
                </div>";
        }

        // If there's a routing error but not an HTMX request, continue with default behavior
        // but log the error
        if ($route_error) {
            // For non-HTMX requests or if we want to show the error in the main layout
            $error_content = "<div class='p-4 bg-red-100 border border-red-400 text-red-700 rounded'>
                <h3 class='font-bold'>Error</h3>
                <p>{$error_message}</p>
            </div>";

            // Return the error in the main layout
            return Edge::render($target_edge, ['view_content' => $error_content]);
        }

        // Load function files using the autoloader
        autobooks_load_function_file($current_page);
        autobooks_load_function_file(str_replace(['.view.','.api.'], '.fn.', $endpoint));

        // Load function files for all parts of the path
        autobooks_load_path_functions(PATH_PARTS, $current_page);

        // Add this code to load system function files if needed
        if (strpos($app_path, 'system') === 0 || TOP_LEVEL === 'system') {
            // Try to load system function files
            $system_page = str_replace('system/', '', $app_path);
            if (empty($system_page)) $system_page = $current_page;
            
            // Load system function file
            autobooks_load_function_file('system/' . $system_page);
        }

        if (!$public) self::checkPermissions($current_page);

        print_rr($endpoint, "launching $target_edge with");

        // Check if the view file has .edge.php extension (for edge template views)
        if (file_exists($endpoint)){
            if (str_ends_with($endpoint, '.edge.php')) {
                print_rr("edge view found");
                // For edge views, use the layout with a callback to render the view
                return Edge::render($target_edge, [
                    'view_content' => Edge::renderView($endpoint, ['function_call' => $function_call]),
                    'function_call' => $function_call
                ]);
            } else {
                print_rr("regular view found");
                // For regular views, use the standard include method
                return Edge::render($target_edge, ['view' => $endpoint, 'function_call' => $function_call]);
            }
        }else{
                tcs_log("View file not found: {$view}", 'router_errors');
                return '
                    <div class="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        <h3 class="font-bold">Error</h3>
                        <p>View file not found: ' . $endpoint  . ' </p>
                    </div>';
        }
    }

    public static function get_route($app_path = '')    {
        $current_page = CURRENT_PAGE;

        // If app_path is empty, default to dashboard
        if (empty($app_path)) {
            $app_path = 'dashboard';
        }

        // Check if this is a system view request
        if (strpos($app_path, 'system') === 0 || $app_path === 'system') {
            // Extract the system view name (after 'system/')
            $system_view = str_replace(['system','system/'], '', $app_path);
            if (empty($system_view)) $system_view = $current_page;

            $system_file_list = [
                FS_SYS_VIEWS . "/{$system_view}/{$current_page}.view.php",
                FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php",
                FS_SYS_VIEWS . "/{$system_view}.view.php",
                FS_SYS_VIEWS . "/{$system_view}.edge.php"
            ];
            print_rr($system_file_list, "system file list");
            foreach ($system_file_list as $file) {
                $file = str_replace('//', '/', $file);
                print_rr($file, "system file");
                if (file_exists($file)) {
                    return [
                        'status' => 'success',
                        'path' => $file
                    ];
                }
            }
        }

        // Regular view file paths (non-system)
        $file_list = [
            "resources/views/{$app_path}.view.php",
            "resources/views/{$app_path}/{$current_page}.view.php",
            "resources/views/{$app_path}.edge.php",
            "resources/views/{$app_path}/{$current_page}.edge.php"
        ];

        foreach ($file_list as $file) {
            if (file_exists(FS_APP_ROOT . $file)) {
                return [
                    'status' => 'success',
                    'path' => $file
                ];
            }
        }

        // If we're here, none of the files were found
        $error_message = "View not found: {$app_path}";
        tcs_log($error_message,'router_errors');
        return [
            'status' => 'error',
            'message' => $error_message
        ];
    }




    public static function get_api($app_path = '') {

        $source_page = SOURCE_PAGE;
        $current_page = CURRENT_PAGE;
        $path_parts = PATH_PARTS;
        $source_path_parts = SOURCE_PATH_PARTS;
        $api_path = ($path_parts[1] == 'system' || $source_path_parts[0] == 'system') ? FS_SYS_API : FS_API;

        // Remove 'api' from the start of path parts if it exists
        if ($path_parts[0] === 'api') {
            array_shift($path_parts);
        }
        if ($path_parts[0] === 'system') {
            array_shift($path_parts);
        }
        // If we have a directory structure in the URL
        if (count($path_parts) > 1) {
            $api_directory = $path_parts[0];  // First part is the directory/module name
            $action = $path_parts[1];         // Second part is the action

            // Look for <directory>.api.php in the api folder
            $api_file = $api_path . "{$api_directory}.api.php";

            print_rr("Looking for API file: {$api_file}");

            if (file_exists($api_file)) {
                return [
                    'status' => 'success',
                    'path' => $api_file
                ];
            } else {
                $error_message = "API file not found: {$api_file}";
                tcs_log($error_message,'router_errors');
                return [
                    'status' => 'error',
                    'message' => $error_message
                ];
            }
        } else {
            // Handle original source page based routing
            $api_result = self::handle_source_page_routing($source_page, $current_page,$path_parts);
            if (is_array($api_result)) {
                return $api_result; // Already in the new format
            } else if ($api_result && file_exists($api_result)) {
                return [
                    'status' => 'success',
                    'path' => $api_result
                ];
            } else {
                $error_message = "API endpoint not found for {$source_page}";
                tcs_log($error_message,'router_errors');
                return [
                    'status' => 'error',
                    'message' => $error_message
                ];
            }
        }
    }

// Helper function to handle the original routing logic
    public static  function handle_source_page_routing($source_page, $current_page)    {
        $view_path = in_array($source_page, SYSTEM_VIEWS) ? FS_SYS_VIEWS : FS_VIEWS;
        $file_name = $source_page . '.api.php';
        $api_path = '/' . tcs_path($view_path . SOURCE_APP_PATH . '/');
        if (file_exists($api_path . '/' . $file_name)) {
            return [
                'status' => 'success',
                'path' => $api_path . '/' . $file_name
            ];
        }
        $view_path = in_array(SOURCE_APP_PATH_PARTS[0], SYSTEM_VIEWS) ? FS_SYS_VIEWS : FS_VIEWS;
        $api_path = '/' . tcs_path($view_path . SOURCE_APP_PATH . '/' . $source_page . '/');
        if (file_exists($api_path . '/' . $file_name)) {
            return [
                'status' => 'success',
                'path' => $api_path . '/' . $file_name
            ];
        }

        $error_message = "API file not found in source page paths: {$api_path}/{$file_name} ";
        tcs_log($error_message,'router_errors');
        return [
            'status' => 'error',
            'message' => $error_message
        ];
    }

    public static function register($route, $controller) {
        self::$routes[$route] = $controller;
    }

    /**
     * Log an error and create a notification
     *
     * @param string $message Error message to log
     * @param string $level Error level (error, warning, info)
     * @return void
     */
    public static function logError($message, $level = 'error') {
        // Log to error log
        error_log("[Router {$level}] {$message}");

        // Log to application log if the function exists
        if (function_exists('tcs_log')) {
            tcs_log($message, 'router_errors');
        }

        // Create a notification if the class exists
        if (class_exists('\Notification\Notification')) {
            try {
                $notification = new \Notification\Notification();
                $notification->create([
                    'type' => 'system_error',
                    'title' => 'Routing Error',
                    'message' => $message,
                    'level' => $level,
                    'context' => [
                        'url' => $_SERVER['REQUEST_URI'],
                        'timestamp' => date('Y-m-d H:i:s')
                    ]
                ]);
            } catch (\Exception $e) {
                error_log("Failed to create notification: " . $e->getMessage());
            }
        }
    }

    public static function checkPermissions($route) {
        $permissions = include('system/route_permissions.php');
        $required_role = $permissions['routes'][$route] ?? $permissions['default'];
        users::requireRole($required_role);
    }
}