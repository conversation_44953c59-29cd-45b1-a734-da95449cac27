@props([
    'route_key' => '',
    'table_name' => '',
    'table_info' => []
])

<div class="max-w-6xl mx-auto p-6">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{{ ucwords(str_replace('_', ' ', $route_key)) }} Settings</h1>
        <p class="text-gray-600 mt-2">Manage data and configuration for the {{ $route_key }} hilt template</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {{-- Left Column: Data Management --}}
        <div class="space-y-6">
            {{-- Current Data Status --}}
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Current Data Status</h2>
                
                <div id="table-summary">
                    @if($table_info['exists'])
                        <div class="p-4 bg-blue-50 border border-blue-200 rounded">
                            <h3 class="font-bold text-blue-800">Table Information</h3>
                            <p><strong>Records:</strong> {{ number_format($table_info['row_count']) }}</p>
                            
                            @if(!empty($table_info['columns']))
                                <p><strong>Columns:</strong> {{ implode(', ', $table_info['columns']) }}</p>
                            @endif
                            
                            @if($table_info['created_at'])
                                <p><strong>Last Updated:</strong> {{ date('Y-m-d H:i:s', strtotime($table_info['created_at'])) }}</p>
                            @endif
                        </div>
                    @else
                        <div class="p-4 bg-gray-100 text-gray-600 rounded">
                            No data table exists yet. Upload CSV data to create the table.
                        </div>
                    @endif
                </div>
            </div>

            {{-- CSV Upload --}}
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Upload CSV Data</h2>
                
                <form hx-post="{{ APP_ROOT }}api/hilt_settings/upload_csv" 
                      hx-target="#table-summary"
                      hx-encoding="multipart/form-data"
                      class="space-y-4">
                    
                    <input type="hidden" name="route_key" value="{{ $route_key }}">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            CSV File
                        </label>
                        <input type="file" 
                               name="csv_file" 
                               accept=".csv,.txt"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Or paste CSV data
                        </label>
                        <textarea name="csv_data" 
                                  rows="6" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Paste CSV data here..."></textarea>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" 
                               name="replace_all" 
                               value="true"
                               id="replace_all"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="replace_all" class="ml-2 block text-sm text-gray-700">
                            Replace all existing data (otherwise, data will be appended)
                        </label>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button type="submit" 
                                class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Upload Data
                        </button>
                        
                        @if($table_info['exists'] && $table_info['row_count'] > 0)
                            <button type="button"
                                    hx-post="{{ APP_ROOT }}api/hilt_settings/clear_data"
                                    hx-target="#table-summary"
                                    hx-vals='{"route_key": "{{ $route_key }}"}'
                                    hx-confirm="Are you sure you want to clear all data? This cannot be undone."
                                    class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500">
                                Clear All Data
                            </button>
                        @endif
                    </div>
                </form>
            </div>

            {{-- Export Data --}}
            @if($table_info['exists'] && $table_info['row_count'] > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold mb-4">Export Data</h2>
                    <p class="text-gray-600 mb-4">Download current data as CSV file</p>
                    
                    <a href="{{ APP_ROOT }}api/hilt_settings/export_csv?route_key={{ $route_key }}"
                       class="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500">
                        Download CSV
                    </a>
                </div>
            @endif
        </div>

        {{-- Right Column: Configuration & Preview --}}
        <div class="space-y-6">
            {{-- Sample Data Preview --}}
            @if(!empty($table_info['sample_data']))
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold mb-4">Data Preview</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    @foreach($table_info['columns'] as $column)
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ ucwords(str_replace('_', ' ', $column)) }}
                                        </th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach(array_slice($table_info['sample_data'], 0, 5) as $row)
                                    <tr>
                                        @foreach($table_info['columns'] as $column)
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $row[$column] ?? '' }}
                                            </td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($table_info['row_count'] > 5)
                        <p class="text-sm text-gray-500 mt-2">
                            Showing 5 of {{ number_format($table_info['row_count']) }} records
                        </p>
                    @endif
                </div>
            @endif

            {{-- Template Information --}}
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Template Information</h2>
                
                <div class="space-y-3">
                    <div>
                        <span class="font-medium text-gray-700">Route Key:</span>
                        <span class="text-gray-900">{{ $route_key }}</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-700">Database Table:</span>
                        <span class="text-gray-900 font-mono text-sm">{{ $table_name }}</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-700">Template Type:</span>
                        <span class="text-gray-900">Hilt (Database-driven)</span>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <h3 class="font-medium text-gray-700 mb-2">Quick Actions</h3>
                    <div class="space-y-2">
                        <a href="{{ APP_ROOT }}{{ $route_key }}" 
                           class="inline-block text-blue-600 hover:text-blue-800 text-sm">
                            → View Template
                        </a>
                        <br>
                        <a href="{{ APP_ROOT }}navigation" 
                           class="inline-block text-blue-600 hover:text-blue-800 text-sm">
                            → Back to Navigation
                        </a>
                    </div>
                </div>
            </div>

            {{-- Help & Documentation --}}
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Help & Tips</h2>
                
                <div class="space-y-3 text-sm text-gray-600">
                    <div>
                        <h4 class="font-medium text-gray-800">CSV Format</h4>
                        <p>Upload CSV files with headers in the first row. Data will be stored as JSON for flexibility.</p>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-800">Data Updates</h4>
                        <p>Choose "Replace all" to clear existing data, or leave unchecked to append new records.</p>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-800">Template Features</h4>
                        <p>The hilt template supports search, pagination, and automatic column detection.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Notification area for HTMX responses --}}
<div id="notification-area"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle file input change to clear textarea
    const fileInput = document.querySelector('input[name="csv_file"]');
    const textArea = document.querySelector('textarea[name="csv_data"]');
    
    if (fileInput && textArea) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                textArea.value = '';
            }
        });
        
        textArea.addEventListener('input', function() {
            if (this.value.trim()) {
                fileInput.value = '';
            }
        });
    }
});
</script>
