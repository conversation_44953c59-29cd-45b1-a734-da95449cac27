<?php

/**
 * @param string $i
 * @param $l
 * @param $co
 * @param $fl
 * @param $e
 * @param $r
 * @return string|void
 * @throws Exception
 */
function print_rr($input = null, string|null $label = null, bool|null $comment_out = true, bool|null $full = null, $throw_ex = false, $return = false, mixed $i = '', $l = null, bool $co = true, bool $fl = null, bool $e = false, bool $r = false) {
    /*ob_start();
    $input = $input ?? $i;
    $label = $label ?? $l;
    $comment_out = $comment_out ?? $co;
    $full = $full ?? $fl;
    $throw_exception = $throw_ex ?? $e;
    $allowed_admin = ['*************', '************'];
    // Get the user's IP address
    $user_ip = '999.999.999';
    if (isset($_SERVER['REMOTE_ADDR'])) $user_ip = $_SERVER['REMOTE_ADDR'];
    // Check if the user's IP is in the allowed IPs array
    //echo "ip: " . $user_ip  ."allowed admin:" . print_r($allowed_admin, true) . PHP_EOL;
    $eol = $co ? PHP_EOL : PHP_EOL . '<br>';
    if (empty($input)) $input = "<< empty " . gettype($input) . '>>';
    if (is_array($allowed_admin) && in_array($user_ip, $allowed_admin)) {
        $trace = debug_backtrace();
        if ($label) $label .= ': ';
        $label = $label . basename($trace[0]['file']) . ' > ' . $trace[1]['function'] . '() ' . $trace[0]['line'];

        if (!$full && (is_string($input) || is_numeric($input))) {
            echo  $label . ": " . $input . $eol;
            $full = $full ?? false;
        } else {

            echo  $co ? $eol . '******************************************************************************************************************************************************** <pre>' . $eol : '<div class="debug">';
            echo  $label . $eol;
            var_dump($input);
            $full = $full ?? true;
        }
        if ($full) {

            echo  $co ? $eol . '    ---------------------------------------------------------------------------- ' . $eol : '</pre><pre>';          array_shift($trace);
            foreach ($trace as $index => $call) {
                echo  "      Function: {$call['function']}, File: {$call['file']}, Line: {$call['line']}\n";
                if (isset($call['args'])) {
                    echo  "        Arguments: " . $eol;
                    $count = 0;
                    foreach ($call['args'] as $arg) {
                        $arg_text = json_encode($arg);
                        if (strlen($arg_text) > 100) $arg_text = substr($arg_text, 0, 100) . '...';
                        echo  "         " . $count++ . ': ' .  $arg_text . $eol;
                    }
                }
            }
            echo  $co ? $eol . '----------------------------------------------------------------------------' . $eol : '</pre></div>';
        }

        $output = str_replace(['<!--','-->'],'',ob_get_clean() );
        $output = $co ? $eol . '<!-- '  . $output : $output;
        if ($full) $output .= '******************************************************************************************************************************************************** </pre>';
        $output .= $co ? ' -->' . $eol : '';
       
        if ($r) return $output;
        // Check if POOFED is defined

        echo $output;

        if ($throw_exception) throw new Exception('' . $label . ':' . $input);
    }*/
}

function log_message($message,$subject = 'main') {
	$log_file = DIR_FS_CATALOG . "logs/{$subject}_logfile.log"; // Define the path to your log file
	$timestamp = date("Y-m-d H:i:s");
	if(is_array($message)){
		$message = print_r($message,true);
	}
	file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND);
}

function print_re($input, $note = "data: ") {
	//$trace = debug_backtrace();
	$output = '<pre> . ' . PHP_EOL . ' ************************************** ' . PHP_EOL;
	$output .= $note . PHP_EOL;
	print_r($input);
	$output .= '
		trace: ' . PHP_EOL;
	$trace = debug_backtrace();
	foreach ($trace as $index => $call) {
		$output .= "Function: {$call['function']}, File: {$call['file']}, Line: {$call['line']}\n";
		if ($index > 0 && isset($call['args'])) {
			$output .= "Arguments: " . json_encode($call['args']) . "\n";
		}
	}
	$output .= '</pre>';
	return $output;
}

function hasVariations($id) {
	$q = tep_db_query("SELECT `products_variations_id` FROM `products_variations` where `products_id` = " . $id . " limit 1;");
	return tep_db_num_rows($q) > 0;
}


function autodesk_products_getislinked($products_id) {
	$q = tep_db_query("SELECT p2a.products_to_autodesk_catalog_id, pac.offeringName
						FROM products_to_autodesk_catalog p2a
						JOIN products_autodesk_catalog pac ON p2a.products_autodesk_catalog_id = pac.id
						where products_id = " . $products_id . " 
						limit 1;");
	$qa = tep_db_fetch_array($q);
	return tep_db_num_rows($q) > 0 ? $qa['offeringName'] : false;
}

function get_autodesk_product($id) {
	$q = tep_db_query("SELECT id, orderAction, offeringName,accessModel_description, term_description, specialProgramDiscount_code 				
						FROM products_autodesk_catalog
						where id = " . $id . " 
						limit 1;");

	$qa = tep_db_fetch_array($q);

	$link_string = $qa['id'] . ': ' . $qa['orderAction'] . ' ' . $qa['offeringName'] . ' ' .  $qa['accessModel_description'] . ' ' . $qa['term_description'] . ' ' . $qa['specialProgramDiscount_code'];

	return tep_db_num_rows($q) > 0 ? $link_string : false;
}

function tcs_get_products_special_price($product_id,$variations_id = null) {
	$variations_string = $variations_id ? " and variations_id = '" . (int)$variations_id . "'" : '';

    $product_query = tep_db_query("select specials_new_products_price from specials where products_id = '" . (int)$product_id . "' $variations_string  and status = 1");
    $product = tep_db_fetch_array($product_query);
	if (isset($product['specials_new_products_price'])) return $product['specials_new_products_price'];
  }

function tcs_display_price($product_id, $price_raw, $tax_class, $return_vat = true,$attribString = null) {
    global $currencies;
    //print_rr($attribString,'attrib_string');
    $products_attributes = new tcs_product_attributes($product_id,1,$attribString);
    $products_attributes->get_attributes(false);
    $products_attributes->get_variations();

    $attribs = $products_attributes->get_current_selected_attributes();
    $variation = $products_attributes->get_current_selected_variation();
    $specials_price = null;
    //print_rr(['variation' => $variation,'attribs' => $attribs],'variation baby');
    $products_price = $price_raw;
    $products_price_raw = $variation['price'] ?? $price_raw;


    $variations_id = $variation['products_variations_id'];
    //echo $products_price;

	//print_rr($attribString,fl:true);

	if ($thePrice = tcs_get_products_special_price($product_id,$variations_id)) {
		$VAT = $currencies->display_price($thePrice + tep_calculate_tax($thePrice, tep_get_tax_rate($tax_class)), tep_get_tax_rate($tax_class)) . ' Inc. VAT';
	} else {
		$VAT = $currencies->display_price($products_price_raw + tep_calculate_tax($products_price_raw, tep_get_tax_rate($tax_class)), tep_get_tax_rate($tax_class)) . ' Inc. VAT';
	}
	if ($new_price = tcs_get_products_special_price($product_id,$variation['products_variations_id'])) {
		$specials_price = $currencies->display_price($new_price, tep_get_tax_rate($tax_class));
	}

	if (@tep_not_null($specials_price)) {
		$VAT = '<br> <span id=productsPriceIncTax>(ex VAT ' . $currencies->display_price($thePrice + tep_calculate_tax($thePrice, tep_get_tax_rate($tax_class)), tep_get_tax_rate($tax_class)) . ' Inc. VAT' . ')</span>';
		$products_price = '<del>' . $currencies->display_price($products_price_raw, tep_get_tax_rate($tax_class)) . '</del> <span class="productSpecialPrice" itemprop="price" id="productInfoPrice" content="' . $currencies->display_raw($new_price, tep_get_tax_rate($tax_class)) . '">' . $currencies->display_price($new_price, tep_get_tax_rate($tax_class)) . '</span>';
	} else {
		$products_price = '<span id="productInfoPrice">' . $currencies->display_price($products_price_raw, tep_get_tax_rate($tax_class)) . '</span>';
		$VAT = '<br> <span id="productsPriceIncTax">(ex VAT ' . $currencies->display_price($products_price_raw + tep_calculate_tax($products_price_raw, tep_get_tax_rate($tax_class)), tep_get_tax_rate($tax_class)) . ' Inc. VAT' . ')</span>';
	}
    //print_rr($products_price,'peepee');
	if ($return_vat) {
		return $products_price . $VAT;
	}

	return $products_price;
}
?>