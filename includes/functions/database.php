<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2013 osCommerce

  Released under the GNU General Public License
*/

// Global variable to hold the PDO instance
$pdo = null;


function tep_db_connect($server = DB_SERVER, $username = DB_SERVER_USERNAME, $password = DB_SERVER_PASSWORD, $database = DB_DATABASE) {
    // function body...
    global $pdo;
    //print_rr($pdo,'$pdo',false);
    try {
        $dsn = "mysql:host=$server;dbname=$database;charset=utf8";
        $options = [
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES   => false,
        ];
        
       print_rr(['dsn' => $dsn, 'username' => $username, 'password' => $password, 'options' => $options],'db', false);

        $pdo = new PDO($dsn, $username, $password, $options);
        $pdo->exec("set session sql_mode=''");
        return $pdo;
    } catch (PDOException $e) {
        die('Connection failed: ' . $e->getMessage());
    }
}

function tep_db_close() {
    global $pdo;
    // In PDO, to close the connection, you set the PDO object to null.
    $pdo = null;
    return true;
}

function tep_db_error($query, $errorInfo) {
    if (defined('STORE_DB_TRANSACTIONS') && (STORE_DB_TRANSACTIONS == 'true')) {
        error_log('ERROR: [' . $errorInfo[1] . '] ' . $errorInfo[2] . "\nQuery: " . $query . "\n", 3, STORE_PAGE_PARSE_TIME_LOG);
    }

    die(print_rr($errorInfo,'Error',false));
}

function tep_db_query($query, $pdo = null,$params = []) {
    if ($pdo === null) {
        global $pdo;
       //print_rr($pdo,'$pdo');
    }

    // Logging if needed
    if (defined('STORE_DB_TRANSACTIONS') && STORE_DB_TRANSACTIONS === 'true') {
        error_log('QUERY: ' . $query . "\n", 3, STORE_PAGE_PARSE_TIME_LOG);
    }

    try {
        // Check if parameters are provided for a prepared statement
        if (!empty($params)) {
            $stmt = $pdo->prepare($query);
            $stmt->execute($params);
        } else {
            $stmt = $pdo->query($query);
        }
        return $stmt;
    } catch (PDOException $e) {
        tep_db_error($query, $e->errorInfo);
    }
}

function tep_db_perform($table, $data, $action = 'insert', $parameters = '', $pdo = null) {
    if ($pdo === null) global $pdo;
    
    if ($action == 'insert') {
        $columns = implode(', ', array_keys($data));
        $placeholders = '';
        $values = [];

        foreach ($data as $key => $value) {// Check if the value looks like an SQL function
            if (is_string($value) && strpos($value, 'now()') !== false) { // Check if the value looks like an SQL function
                $placeholders .= "$value, ";
            } else {
                $placeholders .= '?, ';
                $values[] = $value;
            }            
        }
        $placeholders = rtrim($placeholders, ', ');
        $query = "INSERT INTO $table ($columns) VALUES ($placeholders)";
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute($values);
            return $stmt;
        } catch (PDOException $e) {
            tep_db_error($query, $e->errorInfo);
        }
    } elseif ($action == 'update') {
        $set = '';
        $values = [];

        foreach ($data as $column => $value) {
            if (is_string($value) && strpos($value, '()') !== false) { // Check if the value looks like an SQL function
                $set .= "$column = $value, ";
            } else {
                $set .= "$column = ?, ";
                $values[] = $value;
            }
        }
        $set = rtrim($set, ', ');
        $query = "UPDATE $table SET $set WHERE $parameters";
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute($values);
            return $stmt;
        } catch (PDOException $e) {
            tep_db_error($query, $e->errorInfo);
        }
    }
}


function tep_db_fetch_array($stmt) {
    try {
        return $stmt->fetch();
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}

function tep_db_fetch_all($stmt) {
    try {
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}
function tep_db_fetch_col($stmt) {
    try {
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}

function tep_db_num_rows($stmt) {
    if(!$stmt) return false;
    try {
        return $stmt->rowCount();
    } catch (PDOException $e) {
        tep_db_error('Num Rows', $e->errorInfo);
    }
}

function tep_db_data_seek($stmt, $row_number) {
  tep_db_data_seek_pdo($stmt, $row_number);
}

function tep_db_data_seek_pdo($pdo_statement, $row_number) {
  // Fetch all results into an array
  $results = $pdo_statement->fetchAll(PDO::FETCH_ASSOC);
  
  // Check if the desired row exists
  if (isset($results[$row_number])) {
      // Return the desired row
      return $results[$row_number];
  } else {
      // Return false if the row does not exist
      return false;
  }
}

function tep_db_insert_id($pdo = null) {
    if ($pdo === null) {
        global $pdo;
    }
    try {
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        tep_db_error('Insert ID', $e->errorInfo);
    }
}

function tep_db_free_result($stmt) {
    try {
        $stmt = null;
        return true;
    } catch (PDOException $e) {
        tep_db_error('Free Result', $e->errorInfo);
    }
}

function tep_db_fetch_object($stmt) {
    try {
        return $stmt->fetchObject();
    } catch (PDOException $e) {
        tep_db_error('Fetch Object', $e->errorInfo);
    }
}

function tep_db_input($string, $pdo = null) {
    if ($pdo === null) global $pdo;
    try {
        return substr($pdo->quote($string), 1, -1); // remove the wrapping quotes as tep_db_input() didn't add them
    } catch (PDOException $e) {
        tep_db_error('DB Input', $e->errorInfo);
    }
}

function tep_db_output($string) {
  return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
function tep_db_prepare_input($string) {
  if (is_string($string)) {
      return trim($string);
  } elseif (is_array($string)) {
      foreach ($string as $key => $value) {
          $string[$key] = tep_db_prepare_input($value);
      }
  }
  return $string;
}

function tep_db_affected_rows($stmt) {
    return $stmt->rowCount();
  }

  
  function tep_db_get_server_info($pdo = null) {
    if ($pdo === null) global $pdo;

    // Assuming $$link is a PDO instance
    $stmt = $pdo->query("SELECT VERSION()");
    $version = $stmt->fetchColumn();

    return $version;
}

function tcs_db_build_criteria(array $criteria, array $schema): array {
    $where = $limit = $begin = $order_by = $end = "";
    $count = 0;
    $query_columns = $wheres = $multi_wheres = $searches = [];
    $query_tables = array_fill_keys(array_keys($schema), null);

    $populate_column = function($column = "") use (&$query_columns,&$query_tables,$schema): bool{
        if ($column == "") return false;
        $db = parse_table_column_name($column,$schema);
        if (!$db) return false;
        $query_columns[ $db['table'].$db['column'] ] = build_sql_select_entry($db['column'],$db['table']);
        print_rr('looking for '.$db['table'] . ' in query_tables ' );
        if ( key_exists($db['table'],$query_tables) ) {
            $query_tables[$db['table']] = $schema[$db['table']]['query'];
            print_rr('  found ');
        }
        print_rr($query_tables,'$populate_column qt');
        return true;
    };
    if ($criteria) {
        if (isset($criteria["search"])) {
            foreach ($criteria["search_columns"] as $column) {
                $populate_column($column);
                $searches[] = "{$column} LIKE '%{$criteria["search"]}%'";
            }
            $wheres[] = '(' . implode(' OR ', $searches) . ')';
        }
        if (isset($criteria["column"])) {
            if (isset($criteria["column"][0]) && isset($criteria["column"][1])) {
                $column = $criteria["column"][0];
                $operator = match ($criteria["column"][1]) {
                    "not_equals" => "<>",
                    "less_than" => "<",
                    "greater_than" => ">",
                    "less_than_or_equals" => "<=",
                    "greater_than_or_equals" => ">=",
                    "like" => "LIKE",
                    "not_like" => "NOT LIKE",
                    "in" => "IN",
                    "not_in" => "NOT IN",
                    "between" => "BETWEEN",
                    "not_between" => "NOT BETWEEN",
                    "null" => "IS NULL",
                    "not_null" => "IS NOT NULL",
                    default => "="
                };
                $data = "";
                if (isset($criteria["column"][2])) {
                    $data = $criteria["column"][2];
                }
                $populate_column($column);
                $column = implode('.',parse_table_column_name($column,$schema));
                $wheres[] = "$column $operator $data";
                print_rr($where,'where where?');
            }
        }
        if (isset($criteria["where"])) {
            if (is_string($criteria["where"])) {
                $where = "WHERE " . $criteria["where"];
            } else {
                foreach ($criteria['where'] as $key => $value) {
                    if ( ( $key == 'AND' || $key == 'OR' || $key == 'NOT' ) ) {
                        foreach ($value as $and_key => $and_value) {
                            $table_column_array = parse_table_column_name($and_value[0],$schema);
                            $table_column = implode('.',$table_column_array);
                            $multi_wheres[] = "{$table_column} {$and_value[1]} '{$and_value[2]}'";
                            $populate_column($and_value[0]);
                        }
                        $wheres[] = '(' . implode(" {$key} ", $multi_wheres) . ')';
                    } else {
                        if (is_array($value) && count($value) == 2) { //deprecated
                            $table_column_array = parse_table_column_name($key, $schema);
                            $table_column = implode('.', $table_column_array);
                            $wheres[] = "{$table_column} {$value[0]} '{$value[1]}'";
                            $populate_column($key);
                        } elseif (is_array($value)) {
                            $table_column_array = parse_table_column_name($and_value[0],$schema);
                            $table_column = implode('.',$table_column_array);
                            $multi_wheres[] = "{$table_column} {$and_value[1]} '{$and_value[2]}'";
                            $populate_column($and_value[0]);
                        }
                    }
                }
            }
        }
        if (count($wheres) > 0) $where = 'WHERE ' . implode(' AND ', $wheres);

        if (isset($criteria["order_by"])) {
            if (is_string($criteria["order_by"])){
                $order_by = "ORDER BY {$criteria['order_by']}";
                $column = $criteria['order_by'];
                if (strpos(trim($column),' ')) $column = explode(" ",trim($criteria["order_by"]))[0];
                $populate_column($column);
            }else{
                $table_column_array = parse_table_column_name($criteria["order_by"]["column"],$schema);
                $table_column = implode('.',$table_column_array);
                $order_by = "ORDER BY {$table_column} {$criteria["order_by"]["direction"]}";
                $populate_column($criteria['order_by']['column']);
            }
        }
          if (isset($criteria["begin"])) $begin = " BEGIN {$criteria["begin"]}";
          if (isset($criteria["end"])) {
              if (isset($criteria["limit"]) && $criteria["end"] > $criteria['limit']) {
                  $criteria["end"] = $criteria["limit"];
              }
              $end = " END {$criteria["end"]}";
          }
          if (isset($criteria["limit"])) $limit = " LIMIT {$criteria['limit']}";

        //print_rr( "{$where} {$order_by} {$begin} {$end} {$limit}",'tcs_db_build_criteria end');
        return  ["{$where} {$order_by} {$begin} {$end} {$limit}", $query_columns, $query_tables];
    }
    return ["", $query_columns, $query_tables];
}

function tcs_db_build_tables($data,$schema,$query_columns = [],$query_tables = []): array {
    print_rr($query_columns,'tcs_db_build_tables_cols');
    print_rr($query_tables,'tcs_db_build_tables_tables');
    print_rr($data,'tcs_db_build_tables data');
    foreach (array_filter($data) as $column) {
        if (trim($column) == "") continue;
        $db = parse_table_column_name($column,$schema);
        print_rr(i:$column,label:'tcs_db_build_tables db',full:false);
        if ( isset($schema[$db['table']]["extra_fields"][$db['column']]) ) {
            $query_columns[$db['table'].$db['column']] = $schema[$db['table']]["extra_fields"][$db['column']]['sql'];
            continue;
        }
        $query_columns[$db['table'].$db['column']] = build_sql_select_entry($db['column'],$db['table']);
        if ($db['table'] && !isset($query_tables[$db['table']])) $query_tables[$db['table']] = $schema[$db['table']]['query'];
    }
    print_rr($query_columns,'tcs_db_build_tables end');
    if (count($query_columns) === 0) $query_columns[] = ' * ';
    return [
        'columns' => implode(", ", $query_columns ),
        'column_count' => count($query_columns),
        'tables' => implode(" ", array_filter($query_tables) ),
        'table_count' => count($query_tables)
    ];
}

function build_sql_select_entry($column,$table): string {
    return "{$table}.{$column} AS {$table}_{$column}";
}

function build_select_query($db_data, $schema, $criteria, $get_distinct = false):array {

    [$criteria_string,$criteria_cols,$criteria_tabs] = tcs_db_build_criteria( $criteria,$schema);

    $table_data = tcs_db_build_tables($db_data, $schema, $criteria_cols, $criteria_tabs);
    print_rr(input: $table_data);
    if (count($db_data) == 0) {
        $table_data['columns'] = ' * ';
    }
    if (!isset($table_data['tables']) || (is_array($table_data['columns']) && count($table_data['tables']) == 0)) $table_data['tables'] = $table_data_schema[$db_data['db_table']]['query'];
    $distinct = $get_distinct ? "DISTINCT " : "";
    return ['text' => "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']} {$criteria_string}",'columns' => $table_data['columns'], 'tables' => $table_data['tables'], 'column_count' => $table_data['column_count']];

};
function parse_table_column_name($column_name,$schema) {
    if (!preg_match('/^[a-zA-Z0-9]+[._][0-9a-zA-Z_.]+$/', $column_name)) return false;
    $separator = strpos($column_name, '.') ? '.' : '_';
    $db_field = explode($separator, $column_name,2);

    if (!isset($schema[ $db_field[0] ] ) ) return ['table' => '', 'column' => $column_name];
    return ['table' => $db_field[0], 'column' => $db_field[1]];
}

function tcs_db_query(string $query, array|null $params = []) {
    print_rr($query);
    return tep_db_fetch_all(tep_db_query(query: trim(preg_replace('/\s+/', ' ', $query)), params: $params));
}
function tcs_db_query_col(string $query, array $params = []) {
    print_rr($query);
    return tep_db_fetch_col(tep_db_query(query: trim(preg_replace('/\s+/', ' ', $query)), params: $params));
}

function tep_db_fetch_assoc($stmt) {
    try {
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}
?>