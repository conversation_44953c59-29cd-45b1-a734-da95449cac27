<?php

use Guzzle<PERSON>ttp\Client;

use GuzzleHttp\Exception\RequestException;


use function PHPSTORM_META\type;



define('ADWS_CLIENT_ID', 'yVuCvVbvOuFUr2QBWxv2gNjYfDQQqX1I');
// old F8n4XeeOS4nyXUJmlS1LJ9Z28qPlAt39
define('ADWS_CLIENT_SECRET', 'mIB07tfpksrAhxrA');
// $clientSecret = 'GG9bqyaKINbpscPN';

define('ADWS_CALLBACK_URL', 'www.cadservices.co.uk/adwsapi.php');
define('ADWS_CSN', '5103159758');
define('ADWS_BASE_URL', 'https://enterprise-api.autodesk.com');

define('ADWS_TOKEN_ENDPOINT', ADWS_BASE_URL  . '/v2/oauth/generateaccesstoken');
// $tokenEndpoint = '/v2/oauth/generateaccesstoken';


class AutodeskAuthenticator {
    public function __construct(
        private $client_id = null,
        private $client_secret = null,
        private $token_endpoint = null,
        private $accessToken = null,
        private $httpClient = null,
        private $csn = null,
        private $callback_url = null,
        private $debug = false,
        public $debugLog = []
    ) {
        $this->client_id = ADWS_CLIENT_ID;
        $this->client_secret = ADWS_CLIENT_SECRET;
        $this->token_endpoint = ADWS_TOKEN_ENDPOINT;
        $this->csn = ADWS_CSN;
        $this->callback_url = ADWS_CALLBACK_URL;
        $this->httpClient = new Client();
        $this->debug = $debug;
        $this->debugLog = [];
    }

    public function authenticate() {

        /*
        * Create Signature
        */

        $time_stamp = strtotime("now");
        $base_str = $this->callback_url . $this->client_id . $time_stamp;
        $hmacsha256 = hash_hmac('sha256', $base_str, $this->client_secret, true);
        $signature = base64_encode($hmacsha256);

        /* 
        * Create Authorization
        */

        $base_64_message = $this->client_id . ":" . $this->client_secret;

        $base_64_encoded = base64_encode($base_64_message);


        $headers = [
            "Authorization" => "Basic " . $base_64_encoded,
            "cache-control" => "no-cache",
            "signature" => $signature,
            "CSN" => $this->csn,
            "timestamp" => $time_stamp
        ];

        $query = [
            "grant_type" => "client_credentials"
        ];


        $this->debugLog = [
            "Headers" => $headers,
            "Form Params" => $query
        ];


        if ($this->debug) return;

        try {
            $response = $this->httpClient->post($this->token_endpoint, [
                'headers' => $headers,
                'query' => $query
            ]);


            $responseBody = json_decode($response->getBody(), true);


            if (isset($responseBody['access_token'])) {
                $this->accessToken = $responseBody['access_token'];
            } else {
                throw new Exception('Authentication Failed: ' . $responseBody['error']);
            }
        } catch (RequestException $e) {
            throw new Exception('Request Error: ' . $e->getMessage());
        }
    }

    public function getAccessToken() {
        if (!$this->accessToken) {
            $this->authenticate();
        }
        return $this->accessToken;
    }

    public function getSecret() {
        return $this->client_secret;
    }
}

class AutodeskAPI {
    public $authenticator;
    private $httpClient;
    private $debug;
    private $csn;
    public $debugLog;
    public $current_quotes;

    public function __construct($debug = false) {
        $this->authenticator = new AutodeskAuthenticator();
        $this->httpClient = new Client();
        $this->csn = ADWS_CSN;
        $this->debug = $debug;
    }


    private function api_call($connection) {
        $endpoint = ADWS_BASE_URL . $connection['endpoint'];
        $method = $connection['method'];
        $timestamp = time();
        $accessToken = $this->authenticator->getAccessToken();
        $this->debugLog['log']['auth'] = $this->authenticator->debugLog;
        $signature = $this->generateSignature(ADWS_CALLBACK_URL, $accessToken, $timestamp);
        $Authorization = "Bearer {$accessToken}";
        $api = substr(str_replace('/', '_', $connection['endpoint']), 1);
        $headers = ['Authorization' => $Authorization, 'timestamp' => $timestamp, 'signature' => $signature, 'CSN' => $this->csn];
        if (isset($connection['headers'])) {
            foreach ($connection['headers'] as $key => $value) {
                $headers[$key] = $value;
            }
        }
        if (isset($connection['Content-Type'])) $headers['Content-Type'] = $connection['Content-Type'];
        $data['headers'] = $headers;
        if (isset($connection['query'])) $data["query"] = $connection['query'];
        if (isset($connection['json'])) $data['json'] = $connection['json']; /*$payload;                */
        $this->debugLog['log'][$api] = ["Headers" => $headers, "method" => $method, "endpoint" => $endpoint, "data" => $data, "input_connection" => $connection, "json" => isset($connection['json']) ? json_encode($connection['json']) : ''];
        if ($this->debug) return;
        try {
            $response = $this->httpClient->request($method, $endpoint, $data);
            $responseBody = json_decode($response->getBody(), true);
            $responseHeaders = $response->getHeaders();
            $this->debugLog['log'][$api]['response'] = $responseBody;
            $this->debugLog['log'][$api]['responseHeaders'] = $responseHeaders;
            return ['status' => 'success', 'body' => $responseBody, 'response' => $response];
        } catch (RequestException $e) {
            $this->debugLog['log'][$api]['request'] = $e->getRequest();
            $this->debugLog['log'][$api]['response'] = $e->getResponse()->getBody()->getContents();
            return ['status' => 'fail', 'response' => $e->getResponse()];
        }
    }
    private function api_get_autodesk_product_catalog() {
        $connection = ['endpoint' => '/v1/catalog/export', 'method' => 'GET', 'headers' => ['x-adsk-disable-redirect' => 'true']];
        return $this->api_call($connection);
    }

    private function api_get_autodesk_subscriptions() {
        $connection = [
            'endpoint' => 'v2/export/subscriptions', 
            'method' => 'POST', 
            'json' => [
                    'startDateSince' => '2014-01-01',
                    'webhook.url' => HTTP_SERVER . '/adws_api_subscription_export.php'
                ]
            ];
        return $this->api_call($connection);
    }
    private function api_get_autodesk_promotions() {
        $connection = ['endpoint' => '/v1/promotions/export', 'method' => 'GET', 'headers' => ['x-adsk-disable-redirect' => 'true']];
        return $this->api_call($connection);
    }
    private function api_get_opportunity($opportunityNumber = null, $endCustomerCsn = null) {
        if ($opportunityNumber != null) {
            $query = ["opportunityNumber" => $opportunityNumber];
        } else if ($endCustomerCsn != null) {
            $query = ["endCustomerCsn" => $endCustomerCsn];
        } else {
            return ['status' => 'fail', 'message' => 'need opportunityNumber or endCustomerCsn', 'body' => $this->debugLog];
        }
        $connection = ['endpoint' => '/v2/opportunities', 'method' => 'GET', 'query' => $query];
        return $this->api_call($connection);
    }
    private function api_quotes_finalize($quoteNumber) {
        $connection = ["endpoint" => '/v1/quotes/finalize', 'method' => 'PUT', 'Content-Type' => 'application/json', "json" => ["quoteNumber" =>  $quoteNumber, "skipDDACheck" =>  true, "agentAccount" =>   ["accountCsn" => ADWS_CSN], "agentContact" =>  ["email" =>  "<EMAIL>"]]];
        return $this->api_call($connection);
    }
    private function api_quotes_status($transactionId) {
        $connection = ['endpoint' => '/v1/quotes/status', 'method' => 'GET', 'Content-Type' => null, 'query' => ['transactionId' => $transactionId]];
        return $this->api_call($connection);
    }
    public function api_quotes_view($quoteNumber) {
        $connection = ['endpoint' => '/v1/quotes', 'method' => 'GET', 'Content-Type' => null, 'query' => ['filter[quoteNumber]' => $quoteNumber]];
        return $this->api_call($connection);
    }
    private function api_search_autodesk_customers($criteria) {
        $connection = ["endpoint" => '/v1/accounts/search', 'method' => 'POST', 'Content-Type' => 'application/json', 'json' => $criteria];
        return $this->api_call($connection);
    }
    private function database_get_current_quotes($transaction_id = null, $quotes_id = null) {
        $query_sql =
            "SELECT * 
        FROM autodesk_orders a
        LEFT JOIN customers c 
            ON c.customers_id = a.customers_id
        LEFT JOIN orders o 
            ON o.orders_id = a.orders_id";
        $where_sql = "";
        $param = [];
        if ($transaction_id != null) {
            $where_sql .= ' transactionId = :transaction_id';
            $param[':transaction_id'] = $transaction_id;
        };
        if ($quotes_id != null) {
            if ($where_sql != '') $where_sql .= ' AND ';
            $where_sql .= ' quote_id = :quote_id';
            $param[':quote_id'] = $quotes_id;
        }
        if ($where_sql != '') $where_sql = ' WHERE ' . $where_sql;
        $query_sql .= $where_sql . ' order by a.quote_id desc';
        return tep_db_query($query_sql, null, $param);
    }
    private function database_get_orderAction($orders_id, $products_id) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $products_id]);
        if (tep_db_num_rows($result) > 0) {
            $row = tep_db_fetch_array($result);
            return $row['orderAction'];
        }
        return null;
    }

    private function database_update_subscription_export_data($body){
        $query_sql = "INSERT INTO autodesk_storage SET `key` = :key, `value` = :value";
        $result = tep_db_query($query_sql, null, [":key" => 'subscription_export_data', ":value" => json_encode($body)]);        
    }

    private function database_get_product_extra($orders_id, $products_id = null) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id";
        $params = [":orders_id" => $orders_id];
        if ($products_id != null) {
            $query_sql .= " and products_id = :products_id";
            $params[':products_id'] = $products_id;
        }
        $result = tep_db_query($query_sql, null, $params);
        //print_rr($result, 'database_get_product_extra query_sql');
        if ($products_id) return tep_db_fetch_array($result);
        return tep_db_fetch_all($result);
    }
    function database_get_orders_autodesk($orders_id) {
        $query_sql = "SELECT opportunityNumber FROM orders_autodesk WHERE orders_id = :orders_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id]);
        return tep_db_fetch_array($result);
    }

    private function database_get_autodesk_products($products_id = null) {
        $where = "";
        IF ($products_id != null) {
            $where = " and p.products_id = '" . (int) $products_id . "'";
        }
        $querySQL = "
        select * 
        FROM products_autodesk_catalog pac 
            JOIN products_to_autodesk_catalog p2a ON p2a.unique_hash = pac.unique_hash
            JOIN products p ON p2a.products_id = p.products_id
        WHERE
            p.products_price > 0 
            AND pac.srp > 0
            {$where}";
        return tep_db_fetch_all(tep_db_query($querySQL));
    }

    private function database_get_autodesk_variations() {
        $querySQL = "select * FROM products_autodesk_catalog pac JOIN products_variations pv on pv.products_autodesk_catalog_id = pac.id WHERE pac.srp > 0";
        return tep_db_fetch_all(tep_db_query($querySQL));
    }


    public function products_calculate_pricing($product) {
        $price = $product['SRP'];
        $price -= $product['renewalDiscountAmount'] ?? 0;
        $price -= $product['transactionVolumeDiscountAmount'] ?? 0;
        $price -= $product['serviceDurationDiscountAmount'] ?? 0;
        return $price;
    }
    public function database_update_autodesk_pricing($products = null, $variations = null) {
        // Initialize arrays for logging
        $updatedProducts = [];
        $updatedVariations = [];
    
        if ($products == null) {
            $products = $this->database_get_autodesk_products();
        }
    
        // Initialize arrays to store product IDs and price mappings
        $productIds = [];
        $productCaseStatements = [];
    
        // Collect all product prices and ids for the bulk update
        foreach ($products as $product) {
            $currentPrice = $product['products_price'];  // Get the current price
            $newPrice = $this->products_calculate_pricing($product);  // Calculate the new price
            $productIds[] = $product['products_id'];  // Collect the product ID
            $productCaseStatements[] = "WHEN {$product['products_id']} THEN {$newPrice}";
    
            // Log the updated product with old and new prices
            $updatedProducts[] = [
                'product_id' => $product['products_id'],
                'old_price' => $currentPrice,
                'new_price' => $newPrice
            ];
        }
    
        // If no products are found, stop further execution
        if (!empty($productIds)) {
            // Build the bulk update query for products using CASE
            $productIdsList = implode(",", $productIds);
            $productCaseQuery = implode(" ", $productCaseStatements);
    
            $sql_query = "UPDATE `products` 
                          SET `products_price` = CASE `products_id`
                            $productCaseQuery
                          END
                          WHERE `products_id` IN ($productIdsList)";
    
            // Execute the bulk update query for products
            tep_db_query($sql_query);
        }
    
        // If products are still null, fetch variations
        if ($variations == null) {
            $variations = $this->database_get_autodesk_variations();
        }
    
        // Initialize arrays for variations
        $variationIds = [];
        $variationCaseStatements = [];
    
        // Collect all variation prices and ids for the bulk update
        foreach ($variations as $variation) {
            $currentPrice = $variation['price'];  // Get the current price for variations
            $newPrice = $this->products_calculate_pricing($variation);  // Calculate the new price for variations
            $variationIds[] = $variation['products_variations_id'];  // Collect the variation ID
            $variationCaseStatements[] = "WHEN {$variation['products_variations_id']} THEN {$newPrice}";
    
            // Log the updated variation with old and new prices
            $updatedVariations[] = [
                'variation_id' => $variation['products_variations_id'],
                'old_price' => $currentPrice,
                'new_price' => $newPrice
            ];
        }
    
        // If no variations are found, stop further execution
        if (!empty($variationIds)) {
            // Build the bulk update query for variations using CASE
            $variationIdsList = implode(",", $variationIds);
            $variationCaseQuery = implode(" ", $variationCaseStatements);
    
            $sql_query = "UPDATE `products_variations` 
                          SET `price` = CASE `products_variations_id`
                            $variationCaseQuery
                          END
                          WHERE `products_variations_id` IN ($variationIdsList)";
    
            // Execute the bulk update query for variations
            tep_db_query($sql_query);
        }
    
        // Return the log of updated products and variations with old and new prices
        return [
            'updated_products' => $updatedProducts,
            'updated_variations' => $updatedVariations
        ];
    }
    
    
    public function get_product_price($products_id) {
        $product = $this->database_get_autodesk_products($products_id);
        if (empty($product)) return null;        
        $price = $this->products_calculate_pricing($product);
        return $price;
    }
    private function database_update_product_extra($orders_id, $products_id, $extras) {
        if (sizeof($extras) < 1) return ['response' => 'nothing to do'];
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $products_id]);
        $params = [":orders_id" => $orders_id, ":products_id" => $products_id];
        $set_sql = '';
        foreach ($extras as $key => $extra) {
            $params[':' . $key] = $extra;
            if ($set_sql != '') $set_sql .= ', ';
            $set_sql .= $key . ' = ' . ':' . $key;
        }
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO orders_products_autodesk' . ' SET orders_id = :orders_id, products_id = :products_id, ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE orders_products_autodesk' .  ' SET ' . $set_sql . ' WHERE orders_id = :orders_id and products_id = :products_id';
        }
        $response = tep_db_query($query_sql, null, $params);
        //print_rr($query_sql, 'database_update_product_extra');
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }
    private function generateSignature($callbackUrl, $accessToken, $timestamp) {
        $message = $callbackUrl . $accessToken . $timestamp;
        return base64_encode(hash_hmac('sha256', $message, $this->authenticator->getSecret(), true));
    }
    private function get_product_catalog_header_map() {
        return  ['offeringName' => 'offeringName', 'offeringCode' => 'offeringCode', 'offeringId' => 'offeringId', 'intendedUsage.code' => 'intendedUsage_code', 'intendedUsage.description' => 'intendedUsage_description', 'accessModel.code' => 'accessModel_code', 'accessModel.description' => 'accessModel_description', 'servicePlan.code' => 'servicePlan_code', 'servicePlan.description' => 'servicePlan_description', 'connectivity.code' => 'connectivity_code', 'connectivity.description' => 'connectivity_description', 'term.code' => 'term_code', 'term.description' => 'term_description', 'lifeCycleState' => 'lifeCycleState', 'renewOnlyDate' => 'renewOnlyDate', 'discontinueDate' => 'discontinueDate', 'orderAction' => 'orderAction', 'specialProgramDiscount.code' => 'specialProgramDiscount_code', 'specialProgramDiscount.description' => 'specialProgramDiscount_description', 'fromQty' => 'fromQty', 'toQty' => 'toQty', 'currency' => 'currency', 'SRP' => 'SRP', 'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount', 'renewalDiscountPercent' => 'renewalDiscountPercent', 'renewalDiscountAmount' => 'renewalDiscountAmount', 'costAfterRenewalDiscount' => 'costAfterRenewalDiscount', 'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent', 'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount', 'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount', 'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent', 'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount', 'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount', 'effectiveStartDate' => 'effectiveStartDate', 'effectiveEndDate' => 'effectiveEndDate'];
    }
    private function process_customer_data($customer, $include_all = false) {
        $endCustomer = [];/*search for customer account*/
        $cust_search = $this->search_autodesk_customers($customer['id'], $customer['email_address']);/* $endCustomer = $cust_search;//print_rr($cust_search);//print_rr($customer);    */
        $resultsExist = false; /*($cust_search != null && sizeof($cust_search) > 0);*/
        if ($resultsExist) $endCustomer['accountCsn'] = $cust_search['accountCsn'];/*if ( $resultsExist == false || $include_all == true) {*/
        $endCustomer = array_merge($endCustomer, ['isIndividual' => empty($customer['company']) ? "true" : "false", 'addressLine1' => $customer['street_address'], 'addressLine2' => $customer['suburb'], 'city' => $customer['city'], 'stateProvinceCode' => $customer['state'], 'postalCode' => $customer['postcode'], 'countryCode' => is_string($customer['country']) ? "GB" : $customer['country']['iso_code_2']]);/* Only add 'name' if $customer['company'] is not empty*/
        if (!empty($customer['company'])) {
            $endCustomer['name'] = $customer['company'];
        }    /*}*/
        return ['agentContact' => ['email' => '<EMAIL>'], 'endCustomer' => $endCustomer, 'quoteContact' => ['email' => $customer['email_address'], 'firstName' => $customer['firstname'], 'lastName' => $customer['lastname'], 'phone' => $customer['telephone'], 'preferredLanguage' => 'en']];
    }
    private function process_product_data($products, $include_all = false, $products_id = null, $orders_id = null) {
        $quoteData = ['lineItems' => []];
        $lineItems = [];
        //print_rr($products, 'process_product_data');
        foreach ($products as $product) {
            if ($products_id != null && $products_id != $product['id']) {
                continue;
            }
            $autodesk_product = $this->get_autodesk_product_from_catalog($product['id']);
            $autodesk_extras = $this->database_get_product_extra($orders_id, $product['id']);
            //print_rr($autodesk_extras, 'autodesk_extras' . $product['id']);
            $orderAction = $autodesk_extras['orderAction'] ?? $autodesk_product['orderAction'];
            if ($orders_id != null) {
                $db_order_action = $this->database_get_orderAction($orders_id, $product['id']);
                if ($db_order_action != null) $orderAction = $db_order_action;
            }
            $orderAction_has_dda = false;
            if (str_ends_with($orderAction, '_DDA')) {
                $orderAction = str_replace('_DDA', '', $orderAction);
                $orderAction_has_dda = true;
            }
            //print_rr($autodesk_product, 'autodesk_product process_product_data');
            //print_rr($autodesk_extras, 'autodesk_extras process_product_data');
            //print_rr($orderAction, 'db_order_action process_product_data');
            $lineItems = [];
            if (is_array($autodesk_extras)) $autodesk_product = array_merge($autodesk_product, $autodesk_extras);
            $simpleAction = strtolower(str_replace(['_', '-'], '', $orderAction));
            //print_rr($simpleAction, 'db_order_action');
            $lineitems_extra = [];
            switch ($simpleAction) {
                case 'new':
                    break;
                case 'renewal':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => $orderAction, "quantity" => $autodesk_product["quantity"], "subscriptionId" => $autodesk_product["subscriptionId"], "promotionCode" => $autodesk_product["promotionCode"], "offer" => ["term" => ["code" => $autodesk_product["offer"]["term"]["code"], "description" => $autodesk_product["offer"]["term"]["description"]], "accessModel" => ["code" => $autodesk_product["offer"]["accessModel"]["code"], "description" => $autodesk_product["offer"]["accessModel"]["description"]], "intendedUsage" => ["code" => $autodesk_product["offer"]["intendedUsage"]["code"], "description" => $autodesk_product["offer"]["intendedUsage"]["description"]], "connectivity" => ["code" => $autodesk_product["offer"]["connectivity"]["code"], "description" => $autodesk_product["offer"]["connectivity"]["description"]], "servicePlan" => ["code" => $autodesk_product["offer"]["servicePlan"]["code"], "description" => $autodesk_product["offer"]["servicePlan"]["description"]]]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    break;
                case 'switchproduct':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => $orderAction, "quantity" => $autodesk_product["quantity"], "subscriptionId" => $autodesk_product["subscriptionId"], "promotionCode" => $autodesk_product["promotionCode"], "offer" => ["term" => ["code" => $autodesk_product["offer"]["term"]["code"], "description" => $autodesk_product["offer"]["term"]["description"]], "accessModel" => ["code" => $autodesk_product["offer"]["accessModel"]["code"], "description" => $autodesk_product["offer"]["accessModel"]["description"]], "intendedUsage" => ["code" => $autodesk_product["offer"]["intendedUsage"]["code"], "description" => $autodesk_product["offer"]["intendedUsage"]["description"]], "connectivity" => ["code" => $autodesk_product["offer"]["connectivity"]["code"], "description" => $autodesk_product["offer"]["connectivity"]["description"]], "servicePlan" => ["code" => $autodesk_product["offer"]["servicePlan"]["code"], "description" => $autodesk_product["offer"]["servicePlan"]["description"]]]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    break;
                case 'switchterm':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["subscriptionId" => $product['subscriptionId'], "endDate" => date('Y-m-d')];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    $lineitems_extra =  ["subscriptionId" => $product['subscriptionId'], "endDate" => date('Y-m-d')];
                    break;
                case 'coterm':
                    if (isset($autodesk_product['referenceSubscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => "Co-term", "referenceSubscriptionId" => $autodesk_product["referenceSubscriptionId"], "quantity" => $autodesk_product["quantity"], "startDate" => $autodesk_product["startDate"],];
                        $lineitems_extra =  ["referenceSubscriptionId" => $autodesk_product["referenceSubscriptionId"], "startDate" => $autodesk_product["startDate"],];
                    } else {
                        return ["error" => "referenceSubscriptionId ID not found for product " . $product['id']];
                    }
                    break;
                case 'trueup':
                    if (isset($autodesk_product['subscriptionId']) || $include_all == true) {
                        $lineItems = ["action" => "True-Up", "subscriptionId" => $autodesk_product["subscriptionId"], "quantity" => $autodesk_product["quantity"]];
                        $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"]];
                    } else {
                        ["error" => "Subscription ID not found for product " . $product['id']];
                    }
                    break;
                case 'extension':
                    $lineItems = ["action" => "Extension", "subscriptionId" => $autodesk_product["subscriptionId"], "endDate" => $autodesk_product["endDate"]];
                    $lineitems_extra =  ["subscriptionId" => $autodesk_product["subscriptionId"], "endDate" => $autodesk_product["endDate"]];
                default:
                    ["error" => "Action not found for product " . $product['id']];
                    break;
            }
            if ($orderAction_has_dda) {
                $lineitems_extra['opportunityLineItemId'] = $autodesk_product["opportunityLineItemId"];
            }
            $lineitems_new = null;
            //print_rr($orderAction, 'orderAction beforeNew');
            if ($simpleAction == 'new' || $include_all == true) {
                //print_rr($orderAction, 'orderAction pickedNew');
                if (isset($autodesk_product['offeringId'])) {
                    //print_rr($autodesk_product['offeringId'], '$autodesk_product["offeringId"] isset');
                    $lineItems = ['offeringId' => $autodesk_product['offeringId'], 'offeringName' => $autodesk_product['offeringName'], 'offeringCode' => $autodesk_product['offeringCode'], 'action' => $orderAction, 'quantity' => $product['qty'], 'startDate' => date('Y-m-d'), 'offer' => ['term' => ['code' => $autodesk_product['term_code'], 'description' => $autodesk_product['term_description']], 'accessModel' => ['code' => $autodesk_product['accessModel_code'], 'description' => $autodesk_product['accessModel_description']], 'intendedUsage' => ['code' => $autodesk_product['intendedUsage_code'], 'description' => $autodesk_product['intendedUsage_description']],                'connectivity' => ['code' => $autodesk_product['connectivity_code'], 'description' => $autodesk_product['connectivity_description']], 'servicePlan' => ['code' => $autodesk_product['servicePlan_code'], 'description' => $autodesk_product['servicePlan_description']]],];
                }
            }
            if ($include_all) {
                $lineItems['new'] = $lineItems;
                $lineItems['extra'] = $lineitems_extra;
                $lineItems['internal'] = ["products_id" => $product['id']];
            }
            //print_rr($lineItems, 'process_product_data lineItems');
            $quoteData['lineItems'][] = $lineItems;
        }
        return $quoteData;
    }
    private function validate_quote($quote_json) {
    }
    public function autodesk_raw_api_call($data) {
        if (empty($data)) {
            $this->debugLog['log']['error'] = 'No data provided';
            return $this->debugLog;
        }
        try {
            //print_rr($data, 'autodesk_raw_api_call');
            $quote_data = json_decode($data, true);
            $this->debugLog['log']['final_quote_data'] = $quote_data;
            if (empty($quote_data)) {
                $this->debugLog['log']['error'] = 'JSON DECODE ERROR';
                //print_rr($quote_data, 'autodesk_raw_api_call');
                return $this->debugLog;
            }
            $response = $this->api_send_raw_quote($quote_data);
            //print_rr($response, 'autodesk_raw_api_call response');
            $quote = json_decode($response['body'], true);
            $status_response = $response = $this->api_quotes_status($quote['transactionId']);
            $quote_status = json_decode($status_response['body'], true);
        } catch (Exception $e) {
            $this->debugLog['log']['error'] = $e->getMessage();
            return $this->debugLog;
        }
        if ($quote_status['status'] == 'fail') return $this->debugLog;
        $database_response = $this->database_addquote_from_json($data, $quote_status);
        $this->debugLog['log']['database_query'] = $database_response['query_sql'];
        $this->debugLog['log']['database_reponse'] = $database_response['response'];
        //print_rr($this->debugLog);
        return $this->debugLog;
    }
    function api_send_raw_quote($json_data) {
        $connection = ['endpoint' => '/v3/quotes', 'method' => 'POST', 'Content-Type' => 'application/json', 'json' => $json_data];
        return $this->api_call($connection);
    }
    public function customers_update_quote($orders_id, $products_id, $extras) {
        $query = tep_db_query("select * from orders_products_autodesk where orders_id = :orders_id and products_id = :products_id", null, [":orders_id" => $orders_id, ":products_id" => $products_id]);
        if (tep_db_num_rows($query) < 1)  return ['response' => 'nothing to do'];
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id and products_id = :products_id";
        $result = tep_db_query($query_sql, null, [":orders_id" => $orders_id, ":products_id" => $products_id]);
        $params = [":orders_id" => $orders_id, ":products_id" => $products_id];
        $set_sql = '';
        foreach ($extras as $key => $extra) {
            $params[':' . $key] = $extra;
            if ($set_sql != '') $set_sql .= ', ';
            $set_sql .= $key . ' = ' . ':' . $key;
        }
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO orders_products_autodesk' . ' SET orders_id = :orders_id, products_id = :products_id, ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE orders_products_autodesk' .  ' SET ' . $set_sql . ' WHERE orders_id = :orders_id and products_id = :products_id';
        }
        $response = tep_db_query($query_sql, null, $params);
        //print_rr($query_sql, 'database_update_product_extra');
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }
    public function database_addquote_from_json($order, $quote) {
        $query_sql = "SELECT * FROM autodesk_orders WHERE transactionId = :transaction_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":transaction_id" => $quote['transactionId']]);
        $params = [':customers_id' =>  '', ':orders_id' => '', ':quoteStatus' =>  $quote['quoteStatus'], ':transactionId' => $quote['transactionId'], ':quoteNumber' =>  $quote['quoteNumber']];
        $set_sql = 'SET customers_id = :customers_id,orders_id = :orders_id,quoteStatus = :quoteStatus,transactionId = :transactionId,quoteNumber = :quoteNumber';
        $where_sql = ' WHERE orders_id = :orders_id';
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO autodesk_orders' . ' ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE autodesk_orders' . ' ' . $set_sql . ' ' . $where_sql;
        }
        $response = tep_db_query($query_sql, null, $params);
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }
    public function database_addquote_from_transaction_id($orders_id, $customers_id, $quote) {
        $query_sql = "SELECT * FROM autodesk_orders WHERE transactionId = :transaction_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":transaction_id" => $quote['transactionId']]);
        if (!tep_db_num_rows($result)) {
            $query_sql =
                'INSERT INTO autodesk_orders
             SET `customers_id` = :customers_id,
             `orders_id` = :orders_id,
             `quoteStatus` = :quoteStatus,
             `transactionId` = :transactionId,
             `quoteNumber` = :quoteNumber;
            ';
            $params = [':customers_id' =>  $customers_id, ':orders_id' => $orders_id, ':quoteStatus' =>  $quote['quoteStatus'], ':transactionId' => $quote['transactionId'], ':quoteNumber' => $quote['quoteNumber']];
            $response = tep_db_query($query_sql, null, $params);
            return ['response' => $response, 'query_sql' => $query_sql];
        }
    }
    public function database_addquote($orders_id = null, $customers_id = null, $order = null, $quote = null) {
        if ($customers_id == null) $customers_id = $order->customer['id'];
        if ($orders_id == null) $orders_id = $order->info['id'];
        if ($quote == null) return ['response' => 'Error: No quote supplied', 'query_sql' => ''];

        $query_sql = "SELECT * FROM autodesk_orders WHERE transactionId = :transaction_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":transaction_id" => $quote['transactionId']]);
        
        $params = [':customers_id' =>  $customers_id, ':orders_id' => $orders_id, ':quoteStatus' =>  $quote['quoteStatus'], ':transactionId' => $quote['transactionId'], ':quoteNumber' =>  $quote['quoteNumber']];
        $set_sql = 'SET customers_id = :customers_id,orders_id = :orders_id,quoteStatus = :quoteStatus,transactionId = :transactionId,quoteNumber = :quoteNumber';
        $where_sql = ' WHERE orders_id = :orders_id';
        
        if (!tep_db_num_rows($result)) {
            $query_sql = 'INSERT INTO autodesk_orders' . ' ' .  $set_sql;
        } else {
            $query_sql = 'UPDATE autodesk_orders' . ' ' . $set_sql . ' ' . $where_sql;
        }

        $response = tep_db_query($query_sql, null, $params);
        //print_rr($params, '$params');
        return ['response' => 'Updated quote', 'query_sql' => $query_sql];
    }
    public function database_get_quote($quote_id) {
        $query_sql = "SELECT * FROM autodesk_orders WHERE quote_id = :quote_id LIMIT 1";
        $result = tep_db_query($query_sql, null, [":quote_id" => $quote_id]);
        return tep_db_fetch_array($result);
    }
    public function get_autodesk_product_catalog() { /* Configuration*/
        $csv_file_path = DIR_FS_CATALOG . '/feeds/product_catalog.csv'; /* Define the path where you want to save the CSV file*/
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        if ($download) {
            $get_catalog =  $this->api_get_autodesk_product_catalog();
            if ($get_catalog['status'] == 'fail') {
                return $get_catalog;
            } else {
                $response = $get_catalog['response'];
            }
            $response_headers = $response->getHeaders();
            if (is_string($response_headers['Location'][0])) {
                $input_file = file_get_contents($response_headers['Location'][0]);                            /* Download the CSV file and save it to the specified path                */
                file_put_contents($csv_file_path, $input_file);
            } else {
                return ['status' => 'fail', 'response' => $response];
            }
        }
        return $this->import_autodesk_catalog_into_database($csv_file_path);
    }

    public function get_autodesk_subscriptions() { /* Configuration*/
        $csv_file_path = DIR_FS_CATALOG . '/feeds/subscriptions.csv'; /* Define the path where you want to save the CSV file*/
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        if ($download) {
            $get_subscriptions =  $this->api_get_autodesk_subscriptions();
            $response_body = $get_subscriptions['response']['body'];
            $this->database_update_subscription_export_data($response_body);

            return ["status" => "Requested, it's the webhook's problem now"];
        }
        return ["status" => "cached"];           
         
    }

    public function get_autodesk_product_from_catalog($products_id) {
        $query_sql = "SELECT * FROM products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac ON p2a.unique_hash = pac.unique_hash WHERE products_id = :products_id";
        $query = tep_db_query($query_sql, null, [":products_id" => $products_id]);
        return tep_db_fetch_array($query);
    }
    public function get_autodesk_promotions() { /* Configuration*/
        $csv_file_path = DIR_FS_CATALOG . '/feeds/autodesk_promotions.csv'; /* Define the path where you want to save the CSV file        *//* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age < 86400) return ['status' => 'Nothing to do', 'response' => 'File is less than a day old.'];
        }
        $get_catalog =  $this->api_get_autodesk_promotions();
        if ($get_catalog['status'] == 'fail') {
            return $get_catalog;
        } else {
            $response = $get_catalog['response'];
        }
        $response_headers = $response->getHeaders();
        if (is_string($response_headers['Location'][0])) {
            $input_file = file_get_contents($response_headers['Location'][0]);                            /* Download the CSV file and save it to the specified path                */
            file_put_contents($csv_file_path, $input_file);
            return ['status' => 'success', 'response' => $response];
        }
    }
    public function get_current_quotes() {
        $quotes = $this->database_get_current_quotes();
        $this->current_quotes = tep_db_fetch_all($quotes);
        return $this->current_quotes;
    }
    public function get_opportunity($opportunityNumber = null, $endCustomerCsn = null) {
        return $this->api_get_opportunity($opportunityNumber, $endCustomerCsn);
    }
    public function import_autodesk_catalog_into_database($csv_file_path) {    /* Main script execution*/
        $table_name = 'products_autodesk_catalog';
        $pdo = tep_db_connect();
        $insertedLines = 0;
        $updatedLines = 0;
        $skippedLines = 0;
        $updatedReasons = [];
        $insertedReasons = [];
        $skippedReasons = [];
        if (($handle = fopen($csv_file_path, "r")) !== FALSE) {/* Get the CSV header row*/
            $csvHeader = fgetcsv($handle, 1000, ",");/* Define the mapping from CSV headers to table columns*/
            $headerMap = $this->get_product_catalog_header_map();/* Ensure all required columns are present in the CSV*/
            $missingColumns = array_diff(array_keys($headerMap), $csvHeader);
            if (!empty($missingColumns)) {
                die(json_encode(['error' => "CSV is missing the following columns: " . implode(", ", $missingColumns)]));
            }/* Add the unique_hash column to the table*/
            $headerMap['unique_hash'] = 'unique_hash';/* Read the CSV data and insert or update it into the database*/
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $mappedData = [];
                $concatString = '';
                foreach ($headerMap as $csvColumn => $tableColumn) {
                    $index = array_search($csvColumn, $csvHeader);
                    if ($index !== FALSE) {
                        $mappedData[$tableColumn] = $pdo->quote($data[$index]);/* Concatenate specific columns for the hash*/
                        if (in_array($csvColumn, ['offeringId', 'intendedUsage.code', 'accessModel.code', 'servicePlan.code', 'connectivity.code', 'term.code', 'orderAction', 'specialProgramDiscount.code', 'fromQty', 'toQty'])) {
                            $concatString .= $data[$index];
                        }
                    } else {
                        $mappedData[$tableColumn] = 'NULL';
                    }
                }/* Generate the hash and add it to the mapped data*/
                $uniqueHash = hash('sha256', $concatString);
                $mappedData['unique_hash'] = $pdo->quote($uniqueHash);/* Prepare the SQL insert statement with ON DUPLICATE KEY UPDATE*/
                $columns = array_keys($mappedData);
                $values = array_values($mappedData);
                $updateSQL = [];
                foreach ($columns as $column) {
                    if ($column != 'unique_hash') {
                        $updateSQL[] = "$column = VALUES($column)";
                    }
                }
                $columns = implode(", ", $columns);
                $values = implode(", ", $values);
                $updateSQL = implode(", ", $updateSQL);
                $insertSQL = "INSERT INTO $table_name (" . $columns . ") VALUES (" . $values . ") ON DUPLICATE KEY UPDATE " . $updateSQL;/* Execute the insert or update statement*/
                try {
                    $affectedRows = $pdo->exec($insertSQL);
                    preg_match("/VALUES\s*\((.*?)\)\s*ON DUPLICATE KEY UPDATE/", $insertSQL, $matches);
                    $insertedValues = $matches[1];
                    if ($affectedRows == 1) {
                        $insertedLines++;
                        $insertedReasons[] = "Line " . ($insertedLines + $updatedLines + $skippedLines + 1) .  " Affected rows: " . $affectedRows . " SQL: " . $insertedValues; /* line 328*/
                    } elseif ($affectedRows == 2) {
                        $updatedLines++;
                        $updatedReasons[] = "Line " . ($insertedLines + $updatedLines + $skippedLines + 1) .  " Affected rows: " . $affectedRows . " SQL: " . $insertedValues;
                    } else {
                        $skippedLines++;
                        $skippedReasons[] = "Line " . ($insertedLines + $updatedLines + $skippedLines + 1) .  " Affected rows: " . $affectedRows . " SQL: " . $insertedValues;
                    }
                } catch (PDOException $e) {
                    $skippedLines++;
                    $skippedReasons[] = "Line " . ($insertedLines + $updatedLines + $skippedLines + 1) . ": " . $e->getMessage();
                }
            }
            fclose($handle);
            $this->database_update_autodesk_pricing();
        } else {
            die(json_encode(['error' => "Could not open CSV file."]));
        }
        tep_db_close();/* Output the results in JSON format*/
        return ['message' => 'CSV data import completed.', 'inserted_lines' => $insertedLines, 'inserted_reasons' => $insertedReasons, 'updated_lines' => $updatedLines, 'updated_reasons' => $updatedReasons, 'skipped_lines' => $skippedLines, 'skipped_reasons' => $skippedReasons];
    }
    public function products_get_extra($orders_id, $products_id, $include_all = false) {
        $quoteData = [];
        require_once('includes/classes/order.php');
        $order = new Order($orders_id);
        $products = $order->products;
        $productData = $this->process_product_data($products, $include_all, $products_id, $orders_id);
        return $productData;
    }
    public function quotes_create_from_checkout($orders_id, $customers_id, $order) {
        $customer = $order->customer;
        $products = $order->products;
        $quote = $this->quotes_create($orders_id, $customer, $products);/*$quote = json_decode($response->getBody(), true);*/
        $quote['quoteStatus'] = 'Not sent to Autodesk';
        $database_response = $this->database_addquote($orders_id, $customers_id, $order, $quote);
        return $database_response;
    }
    public function quotes_create($orders_id, $include_all = false) {
        $quoteData = [];
        require_once('includes/classes/order.php');
        $quoteData['currency'] = "GBP"; /* Assuming the currency is passed in the customer array*/
        $quoteData['agentAccount'] = ['accountCsn' => ADWS_CSN];/* $quoteData['callbackUrl'] = ADWS_CALLBACK_URL;*/
        $order = new Order($orders_id);
        //print_rr($orders_id, 'orders_id');
        //print_rr($order, 'Order');
        $customer = $order->customer;
        //print_rr($order->customer, 'customer');
        $products = $order->products;
        $orders_autodesk = $this->database_get_orders_autodesk($orders_id);
        if (!empty($orders_autodesk)) $quoteData['opportunityNumber'] = $orders_autodesk['opportunityNumber'];/*$products_extra = $this->database_get_product_extra($orders_id);*/
        $quoteData['currency'] = "GBP"; /* Assuming the currency is passed in the customer array*/
        $quoteData['agentAccount'] = ['accountCsn' => ADWS_CSN];/* $quoteData['callbackUrl'] = ADWS_CALLBACK_URL;*/
        $custData = $this->process_customer_data($customer, $include_all);
        $productData = $this->process_product_data($products, $include_all, null, $orders_id);
        //print_rr($custData, "customer_data");
        //print_rr($productData, "product_data");
        if ($productData == false || $custData == false) {
            return false;
        }
        $quoteData = array_merge($quoteData, $custData);
        $quoteData = array_merge($quoteData, $productData);
        //print_rr($quoteData, 'quotes_create');
        return $quoteData;
    }
    public function quotes_finalize($quoteNumber) {
        return $this->api_quotes_finalize($quoteNumber);
    }
    public function quotes_notsent_edit($orderAction, $orders_id, $products_id, $data) {
        $database = $this->database_update_product_extra(orders_id: $orders_id, products_id: $products_id, extras: $data,);
        $out = $this->products_get_extra($orders_id, $products_id, true);
        //print_rr($database);
        return $out['lineItems'][0];
    }
    public function quotes_notsent_view($orders_id, $changes = null) {
        return $this->quotes_create($orders_id, true);
    }
    public function quotes_send($orders_id) {
        try {
            $quote_data = $this->quotes_create($orders_id);
            //print_rr($quote_data, 'quotes_send quote_data');
            if ($quote_data == false) {
                return $this->debugLog;
            }
            $this->debugLog['log']['final_quote_data'] = $quote_data;
            //print_rr($quote_data, 'quotes_send final_quote_data');
            $response = $this->api_send_quote($quote_data);
            //print_rr($response, 'quotes_send response');
            $quote = $response['body'];
            if ($quote['status'] == 'fail') return $this->debugLog;
            $status_response = $this->api_quotes_status($quote['transactionId']);
            //print_rr($status_response, 'quotes_send status_response');
            $quote_status = $status_response['body'];
        } catch (Exception $e) {
            $this->debugLog['log']['error'] = $e->getMessage();
            return $this->debugLog;
        }
        if ($quote_status['status'] == 'fail') return $this->debugLog;/*$quote = $quote_status['body'];*/
        //print_rr($status_response, 'quotes_send quote_status');
        //print_rr($quote_status, 'quotes_transactionId');
        $database_response = $this->database_addquote($orders_id, null, null, $quote_status);
        $this->debugLog['log']['database_query'] = $database_response['query_sql'];
        $this->debugLog['log']['database_reponse'] = $database_response['response'];
        //print_rr($this->debugLog);
        return $this->debugLog;
    }
    function api_send_quote($quoteData) {
        $connection = ["endpoint" => '/v3/quotes', 'method' => 'POST', 'Content-Type' => 'application/json', 'json' => $quoteData];
        //print_rr($quoteData, 'api_send_quote');
        return $this->api_call($connection);
    }
    public function search_autodesk_customers($customers_id, $email) {
        $params = [":customers_id" => $customers_id, ":email" => $email];
        $cust_search_db = tep_db_query("SELECT * FROM `customers_autodesk` WHERE `customers_email_address` = :email and `customers_id` = :customers_id LIMIT 1", null, $params);
        if (tep_db_num_rows($cust_search_db) > 0) {
            return tep_db_fetch_array($cust_search_db);
        }
        $cust_search = $this->api_search_autodesk_customers(["contactEmail" => $email]);
        //print_rr($this->debugLog, 'search_autodesk_customers');
        $params = [":customers_id" => $customers_id, ":accountcsn" => $cust_search['body']['results'][0]['csn'], ":email" => $email];
        tep_db_query("INSERT INTO `customers_autodesk` SET `accountCsn` = :accountcsn, `customers_email_address`  = :email, `customers_id` = :customers_id", null, $params);
        return $cust_search;
    }
    public function update_product_extra($orders_id, $products_id, $extras) {
        return $this->database_update_product_extra($orders_id, $products_id, $extras);
    }
}
