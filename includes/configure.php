<?php
/*
  $Id$
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

// Define the webserver and path parameters
// * DIR_FS_* = Filesystem directories (local/physical)
// * DIR_WS_* = Webserver directories (virtual/URL)
  $url = $_SERVER['SERVER_NAME'];

  define('HTTP_SERVER', 'https://' . $url);
  define('HTTPS_SERVER','https://' . $url);
  define('ENABLE_SSL', true);
  define('HTTP_COOKIE_DOMAIN', '');
  define('HTTPS_COOKIE_DOMAIN', '');
  define('HTTP_COOKIE_PATH', '/');
  define('HTTPS_COOKIE_PATH', '/');


if (str_contains($url, 'localhost')){
    $url = "www.cadservices.co.uk";
}
$database = $url = str_replace('.','',$url);
$cache_base = 'not set';
 // Detect Windows and set appropriate cache directory
if (PHP_OS_FAMILY === 'Windows' || DIRECTORY_SEPARATOR === '\\') {
    // Windows: Use a temp directory relative to the document root or system temp
    $cache_base = dirname($_SERVER['SCRIPT_FILENAME'], 2) . DIRECTORY_SEPARATOR . 'temp' . DIRECTORY_SEPARATOR;
    define('DIR_WS_HTTP_CATALOG', '/httpdocs/');
    define('DIR_WS_HTTPS_CATALOG', '/httpdocs/');
    define('DEBUG_MODE', true);
} else {
    define('DIR_WS_HTTP_CATALOG', '/');
    define('DIR_WS_HTTPS_CATALOG', '/');
    define('DEBUG_MODE', false);
    // Linux/Unix: Use the original path
        $cache_base = '/var/www/vhosts/cadservices.co.uk/temp/';
}
define('DIR_FS_CACHE', $cache_base . $url . DIRECTORY_SEPARATOR);
echo $cache_base . $url ;
if (str_contains($url, 'localhost')){
    $url = "www.cadservices.co.uk";
}

  define('DIR_FS_CATALOG', dirname($_SERVER['SCRIPT_FILENAME']) . '/');
  define('DIR_FS_DOWNLOAD', DIR_FS_CATALOG . 'download/');
  define('DIR_FS_DOWNLOAD_PUBLIC', DIR_FS_CATALOG . 'pub/');

  define('DB_SERVER', '127.0.0.1');
  
  define('DB_SERVER_USERNAME', $database);
  define('DB_SERVER_PASSWORD', 'S96#1kvYuCGE');
  define('DB_DATABASE', $database);
  define('USE_PCONNECT', 'false');
  define('STORE_SESSIONS', '');
  define('CFG_TIME_ZONE', 'UTC');
  define('ALLOWED_ADMIN_IP_ADDRESSES', ['*************','************']); 
  ?>