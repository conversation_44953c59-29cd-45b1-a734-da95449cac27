<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

  define('MODULE_PAYMENT_PAYPOINT_SECPAY_TEXT_TITLE', 'PayPoint.net SECPay');
  define('MODULE_PAYMENT_PAYPOINT_SECPAY_TEXT_PUBLIC_TITLE', 'Credit Card');
  define('MODULE_PAYMENT_PAYPOINT_SECPAY_TEXT_DESCRIPTION', 'Credit Card Test Info:<br /><br />CC#: ****************<br />Expiry: Any');
  define('MODULE_PAYMENT_PAYPOINT_SECPAY_TEXT_ERROR', 'Credit Card Error!');
  define('MODULE_PAYMENT_PAYPOINT_SECPAY_TEXT_ERROR_MESSAGE', 'There has been an error processing your credit card. Please try again.');
  define('MODULE_PAYMENT_PAYPOINT_SECPAY_TEXT_ERROR_MESSAGE_N', 'Transaction was not authorised. Please try another card.');
  define('MODULE_PAYMENT_PAYPOINT_SECPAY_TEXT_ERROR_MESSAGE_C', 'There was a communications problem in contacing the bank, please try again.');
?>
